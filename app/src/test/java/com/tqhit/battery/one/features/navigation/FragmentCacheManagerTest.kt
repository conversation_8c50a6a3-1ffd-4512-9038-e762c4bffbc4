package com.tqhit.battery.one.features.navigation

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import com.tqhit.battery.one.R
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*

/**
 * Unit tests for FragmentCacheManager.
 * Tests fragment caching, creation, and performance optimization.
 */
class FragmentCacheManagerTest {

    private lateinit var fragmentCacheManager: FragmentCacheManager
    private lateinit var mockFragmentManager: FragmentManager

    @Before
    fun setUp() {
        fragmentCacheManager = FragmentCacheManager()
        mockFragmentManager = mockk(relaxed = true)
    }

    @Test
    fun `initialize sets up manager correctly`() {
        // Act
        fragmentCacheManager.initialize(mockFragmentManager)

        // Assert - No exception thrown, manager is ready
        assertEquals(0, fragmentCacheManager.getCacheSize())
    }

    @Test
    fun `getOrCreateFragment creates new fragment when cache is empty`() {
        // Arrange
        fragmentCacheManager.initialize(mockFragmentManager)

        // Act
        val fragment = fragmentCacheManager.getOrCreateFragment(R.id.chargeFragment)

        // Assert
        assertNotNull(fragment)
        assertTrue(fragment.javaClass.simpleName.contains("StatsChargeFragment"))
        assertEquals(1, fragmentCacheManager.getCacheSize())
        assertTrue(fragmentCacheManager.isFragmentCached(R.id.chargeFragment))
    }

    @Test
    fun `getOrCreateFragment returns cached fragment on second call`() {
        // Arrange
        fragmentCacheManager.initialize(mockFragmentManager)

        // Act
        val fragment1 = fragmentCacheManager.getOrCreateFragment(R.id.chargeFragment)
        val fragment2 = fragmentCacheManager.getOrCreateFragment(R.id.chargeFragment)

        // Assert
        assertSame(fragment1, fragment2)
        assertEquals(1, fragmentCacheManager.getCacheSize())
    }

    @Test
    fun `getOrCreateFragment creates different fragments for different IDs`() {
        // Arrange
        fragmentCacheManager.initialize(mockFragmentManager)

        // Act
        val chargeFragment = fragmentCacheManager.getOrCreateFragment(R.id.chargeFragment)
        val dischargeFragment = fragmentCacheManager.getOrCreateFragment(R.id.dischargeFragment)

        // Assert
        assertNotSame(chargeFragment, dischargeFragment)
        assertEquals(2, fragmentCacheManager.getCacheSize())
        assertTrue(fragmentCacheManager.isFragmentCached(R.id.chargeFragment))
        assertTrue(fragmentCacheManager.isFragmentCached(R.id.dischargeFragment))
    }

    @Test
    fun `getOrCreateFragment handles invalid fragment states`() {
        // Arrange
        fragmentCacheManager.initialize(mockFragmentManager)
        val mockFragment = mockk<Fragment> {
            every { isRemoving } returns true
            every { isDetached } returns false
        }
        
        // Manually add invalid fragment to cache (simulating edge case)
        val cachedFragments = fragmentCacheManager.getCachedFragments().toMutableMap()
        // Since we can't directly access the private cache, we'll test the behavior indirectly
        
        // Act
        val fragment = fragmentCacheManager.getOrCreateFragment(R.id.chargeFragment)

        // Assert
        assertNotNull(fragment)
        assertFalse(fragment.isRemoving)
    }

    @Test
    fun `validateCache removes invalid fragments`() {
        // Arrange
        fragmentCacheManager.initialize(mockFragmentManager)
        
        // Create a valid fragment first
        val fragment = fragmentCacheManager.getOrCreateFragment(R.id.chargeFragment)
        
        // Act
        fragmentCacheManager.validateCache()

        // Assert - Should not remove valid fragments
        assertEquals(1, fragmentCacheManager.getCacheSize())
        assertTrue(fragmentCacheManager.isFragmentCached(R.id.chargeFragment))
    }

    @Test
    fun `getCachedFragments returns correct fragments`() {
        // Arrange
        fragmentCacheManager.initialize(mockFragmentManager)

        // Act
        val fragment1 = fragmentCacheManager.getOrCreateFragment(R.id.chargeFragment)
        val fragment2 = fragmentCacheManager.getOrCreateFragment(R.id.dischargeFragment)
        val cachedFragments = fragmentCacheManager.getCachedFragments()

        // Assert
        assertEquals(2, cachedFragments.size)
        assertTrue(cachedFragments.containsKey(R.id.chargeFragment))
        assertTrue(cachedFragments.containsKey(R.id.dischargeFragment))
        assertSame(fragment1, cachedFragments[R.id.chargeFragment])
        assertSame(fragment2, cachedFragments[R.id.dischargeFragment])
    }

    @Test
    fun `isFragmentCached returns correct status`() {
        // Arrange
        fragmentCacheManager.initialize(mockFragmentManager)

        // Act & Assert
        assertFalse(fragmentCacheManager.isFragmentCached(R.id.chargeFragment))
        
        fragmentCacheManager.getOrCreateFragment(R.id.chargeFragment)
        assertTrue(fragmentCacheManager.isFragmentCached(R.id.chargeFragment))
        assertFalse(fragmentCacheManager.isFragmentCached(R.id.dischargeFragment))
    }

    @Test
    fun `getCacheSize returns correct count`() {
        // Arrange
        fragmentCacheManager.initialize(mockFragmentManager)

        // Act & Assert
        assertEquals(0, fragmentCacheManager.getCacheSize())
        
        fragmentCacheManager.getOrCreateFragment(R.id.chargeFragment)
        assertEquals(1, fragmentCacheManager.getCacheSize())
        
        fragmentCacheManager.getOrCreateFragment(R.id.dischargeFragment)
        assertEquals(2, fragmentCacheManager.getCacheSize())
        
        // Getting same fragment shouldn't increase count
        fragmentCacheManager.getOrCreateFragment(R.id.chargeFragment)
        assertEquals(2, fragmentCacheManager.getCacheSize())
    }

    @Test
    fun `getPerformanceStats returns meaningful statistics`() {
        // Arrange
        fragmentCacheManager.initialize(mockFragmentManager)

        // Act
        fragmentCacheManager.getOrCreateFragment(R.id.chargeFragment)
        fragmentCacheManager.getOrCreateFragment(R.id.chargeFragment) // Cache hit
        val stats = fragmentCacheManager.getPerformanceStats()

        // Assert
        assertTrue(stats.contains("Fragment Cache Stats"))
        assertTrue(stats.contains("Cached:"))
        assertTrue(stats.contains("Created:"))
        assertTrue(stats.contains("Cache Hits:"))
        assertTrue(stats.contains("Hit Rate:"))
    }

    @Test
    fun `clearFragmentCache removes all fragments`() {
        // Arrange
        fragmentCacheManager.initialize(mockFragmentManager)
        fragmentCacheManager.getOrCreateFragment(R.id.chargeFragment)
        fragmentCacheManager.getOrCreateFragment(R.id.dischargeFragment)

        // Act
        fragmentCacheManager.clearFragmentCache()

        // Assert
        assertEquals(0, fragmentCacheManager.getCacheSize())
        assertFalse(fragmentCacheManager.isFragmentCached(R.id.chargeFragment))
        assertFalse(fragmentCacheManager.isFragmentCached(R.id.dischargeFragment))
    }

    @Test
    fun `logFragmentCacheState executes without error`() {
        // Arrange
        fragmentCacheManager.initialize(mockFragmentManager)
        fragmentCacheManager.getOrCreateFragment(R.id.chargeFragment)

        // Act & Assert - Should not throw exception
        fragmentCacheManager.logFragmentCacheState("TEST_CONTEXT")
    }

    @Test
    fun `cleanupUnknownFragments handles empty fragment manager`() {
        // Arrange
        fragmentCacheManager.initialize(mockFragmentManager)
        every { mockFragmentManager.fragments } returns emptyList()

        // Act & Assert - Should not throw exception
        fragmentCacheManager.cleanupUnknownFragments()
    }

    @Test
    fun `fragment creation handles all supported fragment types`() {
        // Arrange
        fragmentCacheManager.initialize(mockFragmentManager)

        // Act & Assert
        val chargeFragment = fragmentCacheManager.getOrCreateFragment(R.id.chargeFragment)
        assertTrue(chargeFragment.javaClass.simpleName.contains("StatsChargeFragment"))

        val dischargeFragment = fragmentCacheManager.getOrCreateFragment(R.id.dischargeFragment)
        assertTrue(dischargeFragment.javaClass.simpleName.contains("DischargeFragment"))

        val healthFragment = fragmentCacheManager.getOrCreateFragment(R.id.healthFragment)
        assertTrue(healthFragment.javaClass.simpleName.contains("HealthFragment"))

        val settingsFragment = fragmentCacheManager.getOrCreateFragment(R.id.settingsFragment)
        assertTrue(settingsFragment.javaClass.simpleName.contains("SettingsFragment"))

        val animationFragment = fragmentCacheManager.getOrCreateFragment(R.id.animationGridFragment)
        assertTrue(animationFragment.javaClass.simpleName.contains("AnimationGridFragment"))

        val othersFragment = fragmentCacheManager.getOrCreateFragment(R.id.othersFragment)
        assertTrue(othersFragment.javaClass.simpleName.contains("OthersFragment"))
    }

    @Test
    fun `fragment creation handles unknown fragment ID`() {
        // Arrange
        fragmentCacheManager.initialize(mockFragmentManager)

        // Act
        val fragment = fragmentCacheManager.getOrCreateFragment(999) // Unknown ID

        // Assert - Should default to AnimationGridFragment
        assertTrue(fragment.javaClass.simpleName.contains("AnimationGridFragment"))
    }
}
