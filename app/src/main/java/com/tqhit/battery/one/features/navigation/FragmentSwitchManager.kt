package com.tqhit.battery.one.features.navigation

import android.util.Log
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import com.tqhit.battery.one.R
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Manages fragment switching logic and transactions.
 * Extracted from DynamicNavigationManager to improve maintainability and separation of concerns.
 * 
 * Responsibilities:
 * - Fragment transaction management (show/hide and replace patterns)
 * - Fragment visibility validation and overlay prevention
 * - Back stack management for navigation flows
 * - Fragment animation coordination
 * - Integration with FragmentLifecycleOptimizer
 * 
 * Following stats module architecture pattern with proper dependency injection.
 */
@Singleton
class FragmentSwitchManager @Inject constructor(
    private val fragmentLifecycleOptimizer: FragmentLifecycleOptimizer
) {
    companion object {
        private const val TAG = "FragmentSwitchManager"
        private const val FRAGMENT_TAG_PREFIX = "nav_fragment_"
    }

    private var fragmentManager: FragmentManager? = null
    private var fragmentContainerId: Int = 0
    private var currentActiveFragment: Fragment? = null
    private var useShowHidePattern = true // Flag to control navigation pattern
    private var navigationStartTime: Long = 0

    /**
     * Initializes the fragment switch manager.
     * 
     * @param fragmentManager The FragmentManager for fragment transactions
     * @param fragmentContainerId The container ID for fragment replacement
     */
    fun initialize(fragmentManager: FragmentManager, fragmentContainerId: Int) {
        this.fragmentManager = fragmentManager
        this.fragmentContainerId = fragmentContainerId
        Log.d(TAG, "FragmentSwitchManager initialized with container ID: $fragmentContainerId")
    }

    /**
     * Switches to the specified fragment using optimized caching.
     * 
     * @param targetFragment The fragment to switch to
     * @param shouldShowTransition Whether to show transition animation
     * @param isDynamicSwitch Whether this is a dynamic battery state switch
     */
    fun switchToFragment(
        targetFragment: Fragment,
        shouldShowTransition: Boolean = true,
        isDynamicSwitch: Boolean = false
    ) {
        navigationStartTime = System.currentTimeMillis()

        val fragmentManager = this.fragmentManager ?: run {
            Log.e(TAG, "DYNAMIC_SWITCHING: FragmentManager not available for fragment switch")
            return
        }

        val targetFragmentName = targetFragment.javaClass.simpleName
        val currentFragmentName = currentActiveFragment?.javaClass?.simpleName ?: "none"

        if (isDynamicSwitch) {
            Log.i(TAG, "DYNAMIC_SWITCHING: Switching from $currentFragmentName to $targetFragmentName")
        } else {
            Log.d(TAG, "FRAGMENT_SWITCH: Switching from $currentFragmentName to $targetFragmentName")
        }

        try {
            if (useShowHidePattern) {
                performShowHideNavigation(targetFragment, shouldShowTransition, isDynamicSwitch)
            } else {
                performReplaceNavigation(targetFragment, shouldShowTransition, isDynamicSwitch)
            }

            val navigationTime = System.currentTimeMillis() - navigationStartTime
            Log.d(TAG, "FRAGMENT_SWITCH: Fragment switch completed in ${navigationTime}ms")

        } catch (e: Exception) {
            Log.e(TAG, "FRAGMENT_SWITCH: Error switching fragment", e)

            // FRAGMENT_LIFECYCLE_FIX: Avoid replace pattern that destroys fragments
            // Instead, try to recover the show/hide pattern with better error handling
            if (useShowHidePattern) {
                Log.w(TAG, "FRAGMENT_SWITCH: Show/hide pattern failed, attempting recovery")
                try {
                    // Try a simplified show/hide approach without animations
                    performSimplifiedShowHideNavigation(targetFragment)
                    Log.i(TAG, "FRAGMENT_SWITCH: Successfully recovered using simplified show/hide pattern")
                } catch (recoveryException: Exception) {
                    Log.e(TAG, "FRAGMENT_SWITCH: Recovery also failed, this indicates a serious fragment manager issue", recoveryException)
                    // Only as last resort, and reset the flag immediately after
                    Log.w(TAG, "FRAGMENT_SWITCH: Using replace pattern as absolute last resort")
                    performReplaceNavigation(targetFragment, false, isDynamicSwitch)
                    // Reset flag to try show/hide again next time
                    useShowHidePattern = true
                }
            }
        }
    }

    /**
     * Performs navigation using show/hide pattern for better performance.
     */
    private fun performShowHideNavigation(
        targetFragment: Fragment,
        shouldShowTransition: Boolean,
        isDynamicSwitch: Boolean
    ) {
        val fragmentManager = this.fragmentManager ?: return
        val fragmentTag = getFragmentTag(targetFragment)

        if (isDynamicSwitch) {
            Log.i(TAG, "DYNAMIC_SWITCHING: Show/Hide navigation for dynamic switch to: ${targetFragment.javaClass.simpleName}")
        } else {
            Log.d(TAG, "FRAGMENT_PERFORMANCE: Show/Hide navigation to: ${targetFragment.javaClass.simpleName}")
        }

        val transaction = fragmentManager.beginTransaction()

        if (shouldShowTransition) {
            transaction.setCustomAnimations(
                android.R.anim.fade_in,
                android.R.anim.fade_out
            )
        }

        // FRAGMENT_LIFECYCLE_FIX: Improved fragment state validation
        // Hide current active fragment if it exists and is different
        currentActiveFragment?.let { activeFragment ->
            if (activeFragment.isAdded && activeFragment != targetFragment && activeFragment.isVisible) {
                transaction.hide(activeFragment)
                Log.d(TAG, "FRAGMENT_PERFORMANCE: Hiding previous fragment: ${activeFragment.javaClass.simpleName}")
            } else if (activeFragment == targetFragment) {
                Log.d(TAG, "FRAGMENT_PERFORMANCE: Target fragment is already active, skipping hide operation")
            }
        }

        // Show or add the target fragment with better state validation
        if (targetFragment.isAdded) {
            if (!targetFragment.isVisible) {
                transaction.show(targetFragment)
                Log.d(TAG, "FRAGMENT_PERFORMANCE: Showing cached fragment: ${targetFragment.javaClass.simpleName}")
            } else {
                Log.d(TAG, "FRAGMENT_PERFORMANCE: Target fragment is already visible, skipping show operation")
            }
        } else {
            transaction.add(fragmentContainerId, targetFragment, fragmentTag)
            fragmentLifecycleOptimizer.registerFragment(targetFragment)
            Log.d(TAG, "FRAGMENT_PERFORMANCE: Adding new fragment: ${targetFragment.javaClass.simpleName}")
        }

        // Handle back stack management and commit appropriately
        val shouldAddToBackStack = handleBackStackManagement(transaction, targetFragment)

        // Commit transaction - use commit() for back stack, commitNow() otherwise
        if (shouldAddToBackStack) {
            transaction.commit()
        } else {
            transaction.commitNow()
        }

        // Update lifecycle state AFTER transaction is committed
        updateFragmentLifecycleStates(targetFragment)

        // Validate fragment visibility
        validateFragmentVisibility()
    }

    /**
     * Performs simplified show/hide navigation for error recovery.
     * This method uses minimal operations to avoid transaction conflicts.
     */
    private fun performSimplifiedShowHideNavigation(targetFragment: Fragment) {
        val fragmentManager = this.fragmentManager ?: return

        Log.d(TAG, "FRAGMENT_RECOVERY: Attempting simplified show/hide navigation to: ${targetFragment.javaClass.simpleName}")

        try {
            // Use immediate transaction without animations or back stack
            val transaction = fragmentManager.beginTransaction()

            // Hide current active fragment if it exists and is different
            currentActiveFragment?.let { activeFragment ->
                if (activeFragment.isAdded && activeFragment != targetFragment && activeFragment.isVisible) {
                    transaction.hide(activeFragment)
                    Log.d(TAG, "FRAGMENT_RECOVERY: Hiding previous fragment: ${activeFragment.javaClass.simpleName}")
                }
            }

            // Show or add the target fragment
            if (targetFragment.isAdded) {
                if (!targetFragment.isVisible) {
                    transaction.show(targetFragment)
                    Log.d(TAG, "FRAGMENT_RECOVERY: Showing cached fragment: ${targetFragment.javaClass.simpleName}")
                }
            } else {
                val fragmentTag = getFragmentTag(targetFragment)
                transaction.add(fragmentContainerId, targetFragment, fragmentTag)
                fragmentLifecycleOptimizer.registerFragment(targetFragment)
                Log.d(TAG, "FRAGMENT_RECOVERY: Adding new fragment: ${targetFragment.javaClass.simpleName}")
            }

            // Commit immediately without back stack
            transaction.commitNow()

            // Update current active fragment reference
            currentActiveFragment = targetFragment
            fragmentLifecycleOptimizer.onFragmentVisible(targetFragment)

            Log.d(TAG, "FRAGMENT_RECOVERY: Simplified navigation completed successfully")

        } catch (e: Exception) {
            Log.e(TAG, "FRAGMENT_RECOVERY: Simplified navigation also failed", e)
            throw e // Re-throw to trigger final fallback
        }
    }

    /**
     * Performs navigation using replace pattern as fallback.
     */
    private fun performReplaceNavigation(
        targetFragment: Fragment,
        shouldShowTransition: Boolean,
        isDynamicSwitch: Boolean
    ) {
        val fragmentManager = this.fragmentManager ?: return

        if (isDynamicSwitch) {
            Log.i(TAG, "DYNAMIC_SWITCHING: Replace navigation for dynamic switch to: ${targetFragment.javaClass.simpleName}")
        } else {
            Log.d(TAG, "FRAGMENT_PERFORMANCE: Replace navigation to: ${targetFragment.javaClass.simpleName}")
        }

        val transaction = fragmentManager.beginTransaction()

        if (shouldShowTransition) {
            transaction.setCustomAnimations(
                android.R.anim.fade_in,
                android.R.anim.fade_out
            )
        }

        transaction.replace(fragmentContainerId, targetFragment)
        transaction.commitAllowingStateLoss()

        // Update current active fragment reference
        currentActiveFragment = targetFragment
        fragmentLifecycleOptimizer.onFragmentVisible(targetFragment)

        if (isDynamicSwitch) {
            Log.i(TAG, "DYNAMIC_SWITCHING: Replace navigation completed for dynamic switch: ${targetFragment.javaClass.simpleName}")
        } else {
            Log.d(TAG, "FRAGMENT_PERFORMANCE: Replace navigation completed for: ${targetFragment.javaClass.simpleName}")
        }
    }

    /**
     * Handles back stack management for fragment transactions.
     * @return true if transaction should be added to back stack, false otherwise
     */
    private fun handleBackStackManagement(transaction: androidx.fragment.app.FragmentTransaction, targetFragment: Fragment): Boolean {
        // Check if this navigation is coming from Others Fragment
        val isFromOthersFragment = getFragmentId(currentActiveFragment) == R.id.othersFragment
        val targetFragmentId = getFragmentId(targetFragment)
        val isNavigatingToChargeDischarge = targetFragmentId == R.id.chargeFragment || targetFragmentId == R.id.dischargeFragment

        // BACK_NAVIGATION_FIX: Improved back stack management
        val shouldAddToBackStack = isFromOthersFragment && isNavigatingToChargeDischarge
        val isNavigatingBackToOthers = (getFragmentId(currentActiveFragment) == R.id.chargeFragment ||
                                       getFragmentId(currentActiveFragment) == R.id.dischargeFragment) &&
                                       targetFragmentId == R.id.othersFragment

        if (shouldAddToBackStack) {
            transaction.addToBackStack("others_to_charge_discharge")
            Log.d(TAG, "BACK_NAVIGATION_FIX: Added transaction to back stack for navigation from Others to ${getFragmentName(targetFragmentId)}")
            return true
        } else if (isNavigatingBackToOthers) {
            Log.d(TAG, "BACK_NAVIGATION_FIX: Navigating back to Others fragment")
        }

        return false
    }

    /**
     * Updates fragment lifecycle states after transaction.
     */
    private fun updateFragmentLifecycleStates(targetFragment: Fragment) {
        // Update lifecycle state for previous fragment
        currentActiveFragment?.let { previousFragment ->
            if (previousFragment != targetFragment && previousFragment.isAdded) {
                fragmentLifecycleOptimizer.onFragmentHidden(previousFragment)
                Log.d(TAG, "FRAGMENT_LIFECYCLE: Notified lifecycle optimizer that ${previousFragment.javaClass.simpleName} is hidden")
            }
        }

        // Update current active fragment reference and notify lifecycle optimizer
        currentActiveFragment = targetFragment
        fragmentLifecycleOptimizer.onFragmentVisible(targetFragment)
    }

    /**
     * Validates and fixes fragment visibility states to ensure only one fragment is visible.
     */
    private fun validateFragmentVisibility() {
        val fragmentManager = this.fragmentManager ?: return

        try {
            val allFragments = fragmentManager.fragments
            val visibleFragments = allFragments.filter { it?.isVisible == true && it.isAdded }
            val currentActiveFragmentName = currentActiveFragment?.javaClass?.simpleName ?: "none"

            Log.d(TAG, "FRAGMENT_VISIBILITY: Validating visibility - Active: $currentActiveFragmentName, Visible count: ${visibleFragments.size}")

            if (visibleFragments.size > 1) {
                Log.w(TAG, "FRAGMENT_VISIBILITY: Multiple fragments visible (${visibleFragments.size}), fixing...")

                val transaction = fragmentManager.beginTransaction()
                visibleFragments.forEach { fragment ->
                    if (fragment != currentActiveFragment) {
                        transaction.hide(fragment)
                        fragmentLifecycleOptimizer.onFragmentHidden(fragment)
                        Log.w(TAG, "FRAGMENT_VISIBILITY: Force hiding ${fragment?.javaClass?.simpleName} to fix overlay")
                    }
                }
                transaction.commitNow()

                Log.d(TAG, "FRAGMENT_VISIBILITY: Fixed multiple visibility issue - only ${currentActiveFragmentName} should now be visible")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error validating fragment visibility", e)
        }
    }

    /**
     * Gets the current active fragment.
     */
    fun getCurrentActiveFragment(): Fragment? = currentActiveFragment

    /**
     * Sets the current active fragment (for external coordination).
     */
    fun setCurrentActiveFragment(fragment: Fragment?) {
        currentActiveFragment = fragment
    }

    /**
     * Gets the fragment ID from a fragment instance.
     */
    private fun getFragmentId(fragment: Fragment?): Int? {
        return when (fragment) {
            is com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment -> R.id.animationGridFragment
            is com.tqhit.battery.one.fragment.main.others.OthersFragment -> R.id.othersFragment
            is com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment -> R.id.chargeFragment
            is com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment -> R.id.dischargeFragment
            is com.tqhit.battery.one.fragment.main.HealthFragment -> R.id.healthFragment
            is com.tqhit.battery.one.fragment.main.SettingsFragment -> R.id.settingsFragment
            else -> {
                Log.w(TAG, "Unknown fragment type: ${fragment?.javaClass?.simpleName}")
                null
            }
        }
    }

    /**
     * Gets a human-readable fragment name for logging.
     */
    private fun getFragmentName(fragmentId: Int?): String {
        return when (fragmentId) {
            R.id.chargeFragment -> "ChargeFragment"
            R.id.dischargeFragment -> "DischargeFragment"
            R.id.animationGridFragment -> "AnimationGridFragment"
            R.id.othersFragment -> "OthersFragment"
            R.id.healthFragment -> "HealthFragment"
            R.id.settingsFragment -> "SettingsFragment"
            else -> "Unknown($fragmentId)"
        }
    }

    /**
     * Generates a unique tag for fragment identification.
     */
    private fun getFragmentTag(fragment: Fragment): String {
        val fragmentId = getFragmentId(fragment) ?: 0
        return "$FRAGMENT_TAG_PREFIX$fragmentId"
    }
}
