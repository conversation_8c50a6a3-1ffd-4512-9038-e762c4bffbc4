package com.tqhit.battery.one.features.navigation

import android.util.Log
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.google.android.material.bottomnavigation.BottomNavigationView
import com.tqhit.battery.one.R
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Coordinates dynamic navigation based on real-time charging state.
 * Refactored to act as a coordinator that delegates to specialized components
 * for improved maintainability and separation of concerns.
 *
 * This class maintains the existing public API while internally using:
 * - BatteryStateHandler for battery monitoring
 * - FragmentSwitchManager for fragment transactions
 * - NavigationStateManager for state management
 * - FragmentCacheManager for performance optimization
 *
 * Following stats module architecture pattern with proper dependency injection.
 */
@Singleton
class DynamicNavigationManager @Inject constructor(
    private val coreBatteryStatsProvider: CoreBatteryStatsProvider,
    private val batteryStateHandler: BatteryStateHandler,
    private val fragmentSwitchManager: FragmentSwitchManager,
    private val navigationStateManager: NavigationStateManager,
    private val fragmentCacheManager: FragmentCacheManager
) {
    companion object {
        private const val TAG = "DynamicNavigationManager"
    }

    // Expose state flows from NavigationStateManager
    val navigationState: StateFlow<NavigationState?> = navigationStateManager.navigationState
    val stateChanges: StateFlow<NavigationStateChange?> = navigationStateManager.stateChanges

    private var bottomNavigationView: BottomNavigationView? = null
    private var isUpdatingNavigation = false
    
    /**
     * Initializes the navigation manager with required components.
     * Must be called before using other methods.
     *
     * @param fragmentManager The FragmentManager for fragment transactions
     * @param bottomNavigationView The BottomNavigationView to manage
     * @param fragmentContainerId The container ID for fragment replacement
     * @param lifecycleOwner The lifecycle owner for coroutine scope
     * @param restoredSelectedItemId The restored selected item ID from saved state (null for fresh start)
     */
    fun initialize(
        fragmentManager: FragmentManager,
        bottomNavigationView: BottomNavigationView,
        fragmentContainerId: Int,
        lifecycleOwner: LifecycleOwner,
        restoredSelectedItemId: Int? = null
    ) {
        Log.d(TAG, "NAVIGATION_RESTORE: Initializing DynamicNavigationManager with restored item: $restoredSelectedItemId")

        try {
            // Initialize all component managers
            navigationStateManager.initialize()
            fragmentCacheManager.initialize(fragmentManager)
            fragmentSwitchManager.initialize(fragmentManager, fragmentContainerId)

            this.bottomNavigationView = bottomNavigationView

            // Start monitoring battery status changes
            startBatteryStatusMonitoring(lifecycleOwner)

            // Set up initial state with restoration context
            setupInitialState(restoredSelectedItemId)

            Log.d(TAG, "NAVIGATION_RESTORE: DynamicNavigationManager initialized successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing DynamicNavigationManager", e)
            throw e
        }
    }

    /**
     * Starts monitoring battery status changes and updates navigation accordingly.
     */
    private fun startBatteryStatusMonitoring(lifecycleOwner: LifecycleOwner) {
        // Start battery state monitoring
        batteryStateHandler.startBatteryStatusMonitoring(lifecycleOwner)

        // Listen for battery state changes and coordinate navigation updates
        lifecycleOwner.lifecycleScope.launch {
            batteryStateHandler.batteryStateChanges.collect { batteryStateChange ->
                Log.d(TAG, "Battery state change received: ${batteryStateChange.newChargingState}")
                handleBatteryStateChange(batteryStateChange)
            }
        }
    }

    /**
     * Sets up the initial navigation state based on current battery status and restored state.
     *
     * @param restoredSelectedItemId The restored selected item ID from saved state (null for fresh start)
     */
    private fun setupInitialState(restoredSelectedItemId: Int? = null) {
        val isCharging = batteryStateHandler.getCurrentChargingState() ?: false

        Log.d(TAG, "NAVIGATION_RESTORE: Setting up initial state - charging: $isCharging, restored item: $restoredSelectedItemId")

        // Delegate to NavigationStateManager
        navigationStateManager.setupInitialState(isCharging, restoredSelectedItemId)

        // Apply initial navigation changes
        val initialState = navigationStateManager.getCurrentState()
        if (initialState != null) {
            applyNavigationChanges(initialState, null)
        }
    }

    /**
     * Handles battery state changes from BatteryStateHandler and coordinates navigation updates.
     */
    private fun handleBatteryStateChange(batteryStateChange: BatteryStateChange) {
        val currentState = navigationStateManager.getCurrentState()
        val isCharging = batteryStateChange.newChargingState

        Log.d(TAG, "DYNAMIC_SWITCHING: Handling battery state change - charging: $isCharging")

        // Determine if we need dynamic fragment switching
        val shouldSwitchFragment = batteryStateHandler.shouldPerformDynamicFragmentSwitch(
            currentState?.activeFragmentId,
            isCharging
        )

        val targetFragmentId = if (shouldSwitchFragment) {
            batteryStateHandler.createDynamicSwitchTarget(isCharging)
        } else {
            null
        }

        // Update navigation state through NavigationStateManager
        navigationStateManager.updateForBatteryStateChange(
            isCharging = isCharging,
            shouldPerformDynamicSwitch = shouldSwitchFragment,
            targetFragmentId = targetFragmentId
        )

        // Apply navigation changes if needed
        val newState = navigationStateManager.getCurrentState()
        if (newState != null && (shouldSwitchFragment || currentState?.isCharging != isCharging)) {
            applyNavigationChanges(newState, currentState)
        }
    }



    /**
     * Gets a human-readable fragment name for logging.
     */
    private fun getFragmentName(fragmentId: Int?): String {
        return when (fragmentId) {
            R.id.chargeFragment -> "ChargeFragment"
            R.id.dischargeFragment -> "DischargeFragment"
            R.id.animationGridFragment -> "AnimationGridFragment"
            R.id.othersFragment -> "OthersFragment"
            R.id.healthFragment -> "HealthFragment"
            R.id.settingsFragment -> "SettingsFragment"
            else -> "Unknown($fragmentId)"
        }
    }

    /**
     * Applies navigation changes to the UI components.
     */
    private fun applyNavigationChanges(newState: NavigationState, previousState: NavigationState?) {
        try {
            // Update fragment if needed
            if (previousState?.activeFragmentId != newState.activeFragmentId) {
                switchToFragment(newState)
            }

            // Update bottom navigation visibility and selection
            updateBottomNavigation(newState)

        } catch (e: Exception) {
            Log.e(TAG, "Error applying navigation changes", e)
        }
    }

    /**
     * Switches to the fragment specified in the navigation state using optimized caching.
     */
    private fun switchToFragment(state: NavigationState) {
        val targetFragmentName = getFragmentName(state.activeFragmentId)
        val currentFragmentName = getFragmentName(fragmentSwitchManager.getCurrentActiveFragment()?.let { navigationStateManager.getFragmentId(it) })

        Log.d(TAG, "DYNAMIC_SWITCHING: Switching from $currentFragmentName to $targetFragmentName")

        try {
            // Get or create the target fragment from cache
            val targetFragment = fragmentCacheManager.getOrCreateFragment(state.activeFragmentId)

            // Determine if this is a dynamic switch
            val isDynamicSwitch = (state.activeFragmentId == R.id.chargeFragment || state.activeFragmentId == R.id.dischargeFragment) &&
                                 (fragmentSwitchManager.getCurrentActiveFragment()?.let { navigationStateManager.getFragmentId(it) } == R.id.chargeFragment ||
                                  fragmentSwitchManager.getCurrentActiveFragment()?.let { navigationStateManager.getFragmentId(it) } == R.id.dischargeFragment)

            // Delegate to FragmentSwitchManager
            fragmentSwitchManager.switchToFragment(
                targetFragment = targetFragment,
                shouldShowTransition = state.shouldShowTransition,
                isDynamicSwitch = isDynamicSwitch
            )

        } catch (e: Exception) {
            Log.e(TAG, "DYNAMIC_SWITCHING: Error switching fragment", e)
        }
    }


    
    /**
     * Updates the bottom navigation view based on the navigation state.
     */
    private fun updateBottomNavigation(state: NavigationState) {
        val bottomNav = this.bottomNavigationView ?: run {
            Log.e(TAG, "NAVIGATION_RESTORE: BottomNavigationView not available")
            return
        }

        try {
            // Prevent infinite loop by setting flag
            isUpdatingNavigation = true

            // Update selected item
            bottomNav.selectedItemId = state.activeFragmentId
            Log.d(TAG, "NAVIGATION_RESTORE: Set selected item to: ${state.activeFragmentId}")

            // Update menu item visibility
            val menu = bottomNav.menu
            val visibleItemsCount = state.visibleMenuItems.size
            Log.d(TAG, "NAVIGATION_RESTORE: Updating menu visibility - total items: ${menu.size()}, visible items: $visibleItemsCount")

            for (i in 0 until menu.size()) {
                val menuItem = menu.getItem(i)
                val shouldBeVisible = state.isMenuItemVisible(menuItem.itemId)
                menuItem.isVisible = shouldBeVisible

                Log.d(TAG, "NAVIGATION_RESTORE: Menu item ${menuItem.itemId} visibility: $shouldBeVisible")
            }

            // Validate final state
            val actualVisibleCount = (0 until menu.size()).count { menu.getItem(it).isVisible }
            Log.d(TAG, "NAVIGATION_RESTORE: Final visible menu items count: $actualVisibleCount")

        } catch (e: Exception) {
            Log.e(TAG, "Error updating bottom navigation", e)
        } finally {
            // Reset flag
            isUpdatingNavigation = false
        }
    }
    
    /**
     * Handles user navigation selection.
     * Returns true if the navigation was handled, false otherwise.
     */
    fun handleUserNavigation(itemId: Int): Boolean {
        Log.d(TAG, "NAVIGATION_INVESTIGATION: === USER NAVIGATION REQUEST START ===")
        Log.d(TAG, "MultiNavigation: Navigation request to fragment ID: $itemId")

        // Log fragment cache state before navigation
        fragmentCacheManager.logFragmentCacheState("BEFORE_NAVIGATION")

        if (!navigationStateManager.isInitialized()) {
            Log.w(TAG, "NAVIGATION_INVESTIGATION: ❌ Navigation manager not initialized")
            Log.w(TAG, "FragmentCache: Navigation manager not initialized - cache state unknown")
            Log.d(TAG, "NAVIGATION_INVESTIGATION: === USER NAVIGATION FAILED (NOT INITIALIZED) ===")
            return false
        }

        // Prevent infinite loop - ignore navigation events triggered by our own updates
        if (isUpdatingNavigation) {
            Log.v(TAG, "NAVIGATION_INVESTIGATION: Ignoring navigation event during update (preventing infinite loop)")
            return true
        }

        // NAVIGATION_STATE_FIX: Perform state synchronization before processing navigation
        performStateSynchronizationCheck()

        val currentState = navigationStateManager.getCurrentState() ?: run {
            Log.e(TAG, "NAVIGATION_INVESTIGATION: ❌ No current navigation state available")
            Log.d(TAG, "NAVIGATION_INVESTIGATION: === USER NAVIGATION FAILED (NO STATE) ===")
            return false
        }

        val currentFragmentName = getFragmentName(currentState.activeFragmentId)
        val targetFragmentName = getFragmentName(itemId)

        Log.d(TAG, "NAVIGATION_INVESTIGATION: From: $currentFragmentName (ID: ${currentState.activeFragmentId})")
        Log.d(TAG, "NAVIGATION_INVESTIGATION: To: $targetFragmentName (ID: $itemId)")
        Log.d(TAG, "NAVIGATION_INVESTIGATION: Current charging state: ${currentState.isCharging}")
        Log.d(TAG, "NAVIGATION_INVESTIGATION: Fragment cache size: ${fragmentCacheManager.getCacheSize()}")
        Log.d(TAG, "NAVIGATION_INVESTIGATION: Current active fragment: ${fragmentSwitchManager.getCurrentActiveFragment()?.javaClass?.simpleName ?: "none"}")

        // Log fragment cache state
        fragmentCacheManager.getCachedFragments().forEach { (id, fragment) ->
            Log.d(TAG, "NAVIGATION_INVESTIGATION: Cache entry - ${getFragmentName(id)}: isAdded=${fragment.isAdded}, isVisible=${fragment.isVisible}, isDetached=${fragment.isDetached}, isRemoving=${fragment.isRemoving}")
        }

        // Special handling for charge/discharge fragments - allow navigation even if not in visible menu
        val isChargeDischargeFragment = itemId == R.id.chargeFragment || itemId == R.id.dischargeFragment

        // Check if the selected item is visible in current state (with exception for charge/discharge fragments)
        if (!currentState.isMenuItemVisible(itemId) && !isChargeDischargeFragment) {
            Log.w(TAG, "NAVIGATION_INVESTIGATION: ❌ User tried to navigate to hidden item: $itemId")
            Log.d(TAG, "NAVIGATION_INVESTIGATION: === USER NAVIGATION FAILED (HIDDEN ITEM) ===")
            return false
        }

        if (isChargeDischargeFragment) {
            Log.i(TAG, "NAVIGATION_INVESTIGATION: 🔋 CHARGE/DISCHARGE NAVIGATION DETECTED")
            Log.i(TAG, "NAVIGATION_INVESTIGATION: Allowing navigation to charge/discharge fragment: $targetFragmentName (not in visible menu but allowed for dynamic switching)")
            Log.i(TAG, "NAVIGATION_INVESTIGATION: Navigation from Others: ${currentState.activeFragmentId == R.id.othersFragment}")
        }

        // Don't update if already on the same fragment
        if (currentState.activeFragmentId == itemId) {
            Log.v(TAG, "NAVIGATION_INVESTIGATION: Already on fragment $targetFragmentName, skipping update")
            return true
        }

        // Special handling for charge/discharge fragments to ensure proper state synchronization
        val isNavigatingToChargeDischarge = itemId == R.id.chargeFragment || itemId == R.id.dischargeFragment
        if (isNavigatingToChargeDischarge) {
            Log.i(TAG, "NAVIGATION_INVESTIGATION: 🔋 PROCESSING CHARGE/DISCHARGE NAVIGATION")
            Log.i(TAG, "NAVIGATION_INVESTIGATION: User navigating to charge/discharge fragment - $targetFragmentName")

            // Get actual battery charging state
            val actualIsCharging = batteryStateHandler.getCurrentChargingState() ?: false

            Log.d(TAG, "NAVIGATION_INVESTIGATION: Battery state analysis:")
            Log.d(TAG, "NAVIGATION_INVESTIGATION:   - Target fragment: $targetFragmentName")
            Log.d(TAG, "NAVIGATION_INVESTIGATION:   - Actual battery charging state: $actualIsCharging")

            try {
                // Delegate to NavigationStateManager for charge/discharge navigation
                navigationStateManager.updateForUserNavigation(
                    targetFragmentId = itemId,
                    isCharging = actualIsCharging,
                    isChargeDischargeFragment = true
                )

                // Apply navigation changes
                val newState = navigationStateManager.getCurrentState()
                if (newState != null) {
                    applyNavigationChanges(newState, currentState)
                }

                Log.i(TAG, "NAVIGATION_INVESTIGATION: ✅ Synchronized navigation state - fragment: $targetFragmentName, charging: $actualIsCharging")

                // Log fragment cache state after navigation
                fragmentCacheManager.logFragmentCacheState("AFTER_CHARGE_DISCHARGE_NAVIGATION")

                Log.d(TAG, "NAVIGATION_INVESTIGATION: === USER NAVIGATION SUCCESS (CHARGE/DISCHARGE) ===")
                return true
            } catch (e: Exception) {
                Log.e(TAG, "NAVIGATION_INVESTIGATION: ❌ Exception in charge/discharge navigation", e)
                Log.e(TAG, "FragmentCache: Exception during charge/discharge navigation - cache state may be corrupted")
                Log.d(TAG, "NAVIGATION_INVESTIGATION: === USER NAVIGATION FAILED (CHARGE/DISCHARGE EXCEPTION) ===")
                return false
            }
        }

        // Handle standard navigation (non-charge/discharge fragments)
        Log.d(TAG, "NAVIGATION_INVESTIGATION: 📱 STANDARD NAVIGATION")
        Log.d(TAG, "NAVIGATION_INVESTIGATION: Standard user navigation - $targetFragmentName")

        try {
            // Delegate to NavigationStateManager for standard navigation
            navigationStateManager.updateForUserNavigation(
                targetFragmentId = itemId,
                isCharging = currentState.isCharging,
                isChargeDischargeFragment = false
            )

            // Apply navigation changes
            val newState = navigationStateManager.getCurrentState()
            if (newState != null) {
                applyNavigationChanges(newState, currentState)
            }

            // Log fragment cache state after navigation
            fragmentCacheManager.logFragmentCacheState("AFTER_STANDARD_NAVIGATION")

            Log.d(TAG, "NAVIGATION_INVESTIGATION: === USER NAVIGATION SUCCESS (STANDARD) ===")
            return true
        } catch (e: Exception) {
            Log.e(TAG, "NAVIGATION_INVESTIGATION: ❌ Exception in standard navigation", e)
            Log.e(TAG, "FragmentCache: Exception during standard navigation - cache state may be corrupted")
            Log.d(TAG, "NAVIGATION_INVESTIGATION: === USER NAVIGATION FAILED (STANDARD EXCEPTION) ===")
            return false
        }
    }

    /**
     * Gets the current navigation state.
     */
    fun getCurrentState(): NavigationState? = navigationStateManager.getCurrentState()

    /**
     * Manually triggers dynamic switching for testing purposes.
     * This method can be used to test the dynamic switching logic.
     */
    fun triggerDynamicSwitchingTest(newChargingState: Boolean) {
        Log.i(TAG, "DYNAMIC_SWITCHING: Manual trigger test - newChargingState: $newChargingState")
        // Create a test battery state change
        val testStateChange = BatteryStateChange(
            previousChargingState = !newChargingState,
            newChargingState = newChargingState,
            timestamp = System.currentTimeMillis()
        )
        handleBatteryStateChange(testStateChange)
    }


    /**
     * Checks if the navigation manager is initialized.
     */
    fun isInitialized(): Boolean = navigationStateManager.isInitialized()

    /**
     * BACK_NAVIGATION_FIX: Handles back navigation state recovery.
     * Called when navigating back to ensure proper fragment state restoration.
     */
    fun handleBackNavigationStateRecovery() {
        Log.d(TAG, "NAVIGATION_STATE_FIX: === BACK NAVIGATION STATE RECOVERY START ===")

        try {
            // NAVIGATION_STATE_FIX: Detect actual visible fragment and sync state
            val actualVisibleFragment = detectActualVisibleFragment()
            val currentState = getCurrentState()

            Log.d(TAG, "NAVIGATION_STATE_FIX: Current state says active: ${getFragmentName(currentState?.activeFragmentId)}")
            Log.d(TAG, "NAVIGATION_STATE_FIX: Actually visible fragment: ${actualVisibleFragment?.javaClass?.simpleName}")

            if (actualVisibleFragment != null && currentState != null) {
                val actualFragmentId = navigationStateManager.getFragmentId(actualVisibleFragment)

                if (actualFragmentId != null && actualFragmentId != currentState.activeFragmentId) {
                    Log.w(TAG, "NAVIGATION_STATE_FIX: ⚠️ STATE DESYNC DETECTED!")
                    Log.w(TAG, "NAVIGATION_STATE_FIX: State says: ${getFragmentName(currentState.activeFragmentId)}")
                    Log.w(TAG, "NAVIGATION_STATE_FIX: Actually visible: ${getFragmentName(actualFragmentId)}")

                    // NAVIGATION_STATE_FIX: Force state synchronization
                    navigationStateManager.synchronizeWithActualState(actualFragmentId)
                    fragmentSwitchManager.setCurrentActiveFragment(actualVisibleFragment)

                    Log.i(TAG, "NAVIGATION_STATE_FIX: ✅ State synchronized to match visible fragment: ${getFragmentName(actualFragmentId)}")

                    // Update bottom navigation to match corrected state
                    bottomNavigationView?.selectedItemId = actualFragmentId

                } else {
                    Log.d(TAG, "NAVIGATION_STATE_FIX: State is already synchronized")
                }

                // If we're back to Others fragment, ensure it's properly refreshed
                if (actualFragmentId == R.id.othersFragment) {
                    val othersFragment = actualVisibleFragment as? com.tqhit.battery.one.fragment.main.others.OthersFragment
                    if (othersFragment != null && othersFragment.isAdded && othersFragment.isVisible) {
                        Log.d(TAG, "NAVIGATION_STATE_FIX: Triggering Others fragment refresh after back navigation")
                        // The Others fragment will handle its own refresh in onResume()
                    }
                }

                Log.d(TAG, "NAVIGATION_STATE_FIX: Back navigation state recovery completed successfully")
            } else {
                Log.w(TAG, "NAVIGATION_STATE_FIX: No visible fragment or current state available for recovery")
            }
        } catch (e: Exception) {
            Log.e(TAG, "NAVIGATION_STATE_FIX: Error during back navigation state recovery", e)
        }

        Log.d(TAG, "NAVIGATION_STATE_FIX: === BACK NAVIGATION STATE RECOVERY END ===")
    }

    /**
     * NAVIGATION_STATE_FIX: Detects the actually visible fragment by examining the fragment container.
     * This helps identify state desynchronization issues.
     */
    private fun detectActualVisibleFragment(): Fragment? {
        return try {
            // Use cached fragments to detect visible fragment
            val cachedFragments = fragmentCacheManager.getCachedFragments()
            Log.d(TAG, "NAVIGATION_STATE_FIX: Scanning ${cachedFragments.size} cached fragments for visibility")

            cachedFragments.values.forEach { fragment ->
                Log.d(TAG, "NAVIGATION_STATE_FIX: Fragment ${fragment.javaClass.simpleName}: visible=${fragment.isVisible}, added=${fragment.isAdded}, hidden=${fragment.isHidden}")
            }

            val visibleFragment = cachedFragments.values.find { it.isVisible && it.isAdded && !it.isHidden }
            Log.d(TAG, "NAVIGATION_STATE_FIX: Detected visible fragment: ${visibleFragment?.javaClass?.simpleName}")

            visibleFragment
        } catch (e: Exception) {
            Log.e(TAG, "NAVIGATION_STATE_FIX: Error detecting visible fragment", e)
            null
        }
    }

    /**
     * NAVIGATION_STATE_FIX: Performs state synchronization check before navigation.
     * This prevents navigation issues caused by state desynchronization.
     */
    private fun performStateSynchronizationCheck() {
        try {
            val actualVisibleFragment = detectActualVisibleFragment()
            val currentState = getCurrentState()

            if (actualVisibleFragment != null && currentState != null) {
                val actualFragmentId = navigationStateManager.getFragmentId(actualVisibleFragment)

                if (actualFragmentId != null && actualFragmentId != currentState.activeFragmentId) {
                    Log.w(TAG, "NAVIGATION_STATE_FIX: ⚠️ PRE-NAVIGATION STATE DESYNC DETECTED!")
                    Log.w(TAG, "NAVIGATION_STATE_FIX: State says: ${getFragmentName(currentState.activeFragmentId)}")
                    Log.w(TAG, "NAVIGATION_STATE_FIX: Actually visible: ${getFragmentName(actualFragmentId)}")

                    // NAVIGATION_STATE_FIX: Force state synchronization
                    navigationStateManager.synchronizeWithActualState(actualFragmentId)
                    fragmentSwitchManager.setCurrentActiveFragment(actualVisibleFragment)

                    Log.i(TAG, "NAVIGATION_STATE_FIX: ✅ Pre-navigation state synchronized to: ${getFragmentName(actualFragmentId)}")
                } else {
                    Log.v(TAG, "NAVIGATION_STATE_FIX: Pre-navigation state check passed")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "NAVIGATION_STATE_FIX: Error during pre-navigation state synchronization", e)
        }
    }

    /**
     * Gets performance statistics for debugging.
     */
    fun getPerformanceStats(): String {
        val cacheStats = fragmentCacheManager.getPerformanceStats()
        val batteryStats = batteryStateHandler.getMonitoringStatus()
        val visibilityStats = "Fragment Visibility - Active: ${fragmentSwitchManager.getCurrentActiveFragment()?.javaClass?.simpleName ?: "none"}"
        return "$cacheStats\n$batteryStats\n$visibilityStats"
    }



}
