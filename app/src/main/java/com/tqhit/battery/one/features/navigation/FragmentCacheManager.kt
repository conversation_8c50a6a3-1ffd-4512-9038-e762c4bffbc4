package com.tqhit.battery.one.features.navigation

import android.util.Log
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import com.tqhit.battery.one.R
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Manages fragment caching and performance optimization.
 * Extracted from DynamicNavigationManager to improve maintainability and separation of concerns.
 * 
 * Responsibilities:
 * - Fragment instance caching for performance optimization
 * - Fragment creation and lifecycle management
 * - Performance metrics tracking (cache hits, creation count)
 * - Cache validation and cleanup
 * - Memory management and fragment state validation
 * 
 * Following stats module architecture pattern with proper dependency injection.
 */
@Singleton
class FragmentCacheManager @Inject constructor() {
    companion object {
        private const val TAG = "FragmentCacheManager"
    }

    // Fragment cache for performance optimization
    private val fragmentCache = mutableMapOf<Int, Fragment>()

    // Performance tracking
    private var fragmentCreationCount = 0
    private var cacheHitCount = 0

    private var fragmentManager: FragmentManager? = null

    /**
     * Initializes the fragment cache manager.
     * 
     * @param fragmentManager The FragmentManager for fragment management
     */
    fun initialize(fragmentManager: FragmentManager) {
        this.fragmentManager = fragmentManager
        Log.d(TAG, "FragmentCacheManager initialized")
    }

    /**
     * Gets or creates a fragment instance with caching for performance optimization.
     * 
     * @param fragmentId The fragment ID to get or create
     * @return The fragment instance
     */
    fun getOrCreateFragment(fragmentId: Int): Fragment {
        // Check cache first
        fragmentCache[fragmentId]?.let { cachedFragment ->
            // FRAGMENT_LIFECYCLE_FIX: More lenient fragment state validation
            // Only recreate if fragment is truly unusable (being removed)
            if (cachedFragment.isRemoving) {
                Log.w(TAG, "FRAGMENT_CACHE: Cached fragment ${cachedFragment.javaClass.simpleName} is being removed, creating new instance")
                // Remove from cache and create new instance
                fragmentCache.remove(fragmentId)
                val newFragment = createFragmentInstance(fragmentId)
                fragmentCache[fragmentId] = newFragment
                fragmentCreationCount++
                Log.d(TAG, "FRAGMENT_CACHE: Created new ${newFragment.javaClass.simpleName} instance to replace removing fragment")
                return newFragment
            } else if (cachedFragment.isDetached && !cachedFragment.isAdded) {
                Log.w(TAG, "FRAGMENT_CACHE: Cached fragment ${cachedFragment.javaClass.simpleName} is detached and not added, creating new instance")
                // Remove from cache and create new instance
                fragmentCache.remove(fragmentId)
                val newFragment = createFragmentInstance(fragmentId)
                fragmentCache[fragmentId] = newFragment
                fragmentCreationCount++
                Log.d(TAG, "FRAGMENT_CACHE: Created new ${newFragment.javaClass.simpleName} instance to replace detached fragment")
                return newFragment
            }

            cacheHitCount++
            Log.d(TAG, "FRAGMENT_CACHE: Cache hit for ${cachedFragment.javaClass.simpleName} (state: added=${cachedFragment.isAdded}, detached=${cachedFragment.isDetached}, removing=${cachedFragment.isRemoving})")
            return cachedFragment
        }

        // Create new fragment if not in cache
        val fragment = createFragmentInstance(fragmentId)
        fragmentCache[fragmentId] = fragment
        fragmentCreationCount++

        Log.d(TAG, "FRAGMENT_CACHE: Created new fragment for ID: $fragmentId (total cached: ${fragmentCache.size})")
        return fragment
    }

    /**
     * Creates a new fragment instance based on fragment ID.
     * 
     * @param fragmentId The fragment ID
     * @return New fragment instance
     */
    private fun createFragmentInstance(fragmentId: Int): Fragment {
        return when (fragmentId) {
            R.id.chargeFragment -> com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment()
            R.id.dischargeFragment -> com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment()
            R.id.healthFragment -> com.tqhit.battery.one.fragment.main.HealthFragment()
            R.id.settingsFragment -> com.tqhit.battery.one.fragment.main.SettingsFragment()
            R.id.animationGridFragment -> com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment()
            R.id.othersFragment -> com.tqhit.battery.one.fragment.main.others.OthersFragment()
            else -> com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment()
        }
    }

    /**
     * Validates cached fragments and removes invalid ones.
     */
    fun validateCache() {
        val invalidFragments = mutableListOf<Int>()

        fragmentCache.forEach { (id, fragment) ->
            if (fragment.isRemoving || fragment.isDetached || !fragment.isAdded) {
                Log.w(TAG, "FRAGMENT_CACHE: Found invalid cached fragment: ${getFragmentName(id)} (removing=${fragment.isRemoving}, detached=${fragment.isDetached}, added=${fragment.isAdded})")
                invalidFragments.add(id)
            }
        }

        // Remove invalid fragments from cache
        invalidFragments.forEach { id ->
            fragmentCache.remove(id)
            Log.d(TAG, "FRAGMENT_CACHE: Removed invalid fragment from cache: ${getFragmentName(id)}")
        }

        if (invalidFragments.isNotEmpty()) {
            Log.d(TAG, "FRAGMENT_CACHE: Cache validation completed - removed ${invalidFragments.size} invalid fragments")
        }
    }

    /**
     * Gets all cached fragments.
     * 
     * @return Map of fragment ID to fragment instance
     */
    fun getCachedFragments(): Map<Int, Fragment> = fragmentCache.toMap()

    /**
     * Checks if a fragment is cached.
     * 
     * @param fragmentId The fragment ID to check
     * @return true if fragment is cached, false otherwise
     */
    fun isFragmentCached(fragmentId: Int): Boolean = fragmentCache.containsKey(fragmentId)

    /**
     * Gets cache size.
     * 
     * @return Number of cached fragments
     */
    fun getCacheSize(): Int = fragmentCache.size

    /**
     * Gets performance statistics for debugging.
     * 
     * @return Performance statistics string
     */
    fun getPerformanceStats(): String {
        val hitRate = if (fragmentCreationCount > 0) {
            (cacheHitCount * 100 / (fragmentCreationCount + cacheHitCount))
        } else {
            0
        }
        return "Fragment Cache Stats - Cached: ${fragmentCache.size}, Created: $fragmentCreationCount, Cache Hits: $cacheHitCount, Hit Rate: $hitRate%"
    }

    /**
     * Logs the current fragment cache state for debugging.
     * 
     * @param context Context description for logging
     */
    fun logFragmentCacheState(context: String) {
        Log.d(TAG, "FragmentCache: === FRAGMENT CACHE STATE ($context) ===")
        Log.d(TAG, "FragmentCache: Cache size: ${fragmentCache.size}")
        Log.d(TAG, "FragmentCache: Fragment creation count: $fragmentCreationCount")
        Log.d(TAG, "FragmentCache: Cache hit count: $cacheHitCount")

        if (fragmentCache.isEmpty()) {
            Log.d(TAG, "FragmentCache: Cache is empty")
        } else {
            fragmentCache.forEach { (id, fragment) ->
                val fragmentName = getFragmentName(id)
                val instanceHash = fragment.hashCode()
                Log.d(TAG, "FragmentCache: [$fragmentName] instance=$instanceHash, added=${fragment.isAdded}, visible=${fragment.isVisible}, hidden=${fragment.isHidden}, detached=${fragment.isDetached}, removing=${fragment.isRemoving}")

                // Special logging for DischargeFragment to track ViewModel preservation
                if (fragment is com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment) {
                    Log.d(TAG, "MultiNavigation: DischargeFragment instance hash: $instanceHash")
                    try {
                        // Use reflection to get ViewModel hashCode if possible
                        val viewModelField = fragment.javaClass.getDeclaredField("viewModel")
                        viewModelField.isAccessible = true
                        val viewModel = viewModelField.get(fragment)
                        if (viewModel != null) {
                            Log.d(TAG, "MultiNavigation: DischargeFragment ViewModel hash: ${viewModel.hashCode()}")
                        } else {
                            Log.d(TAG, "MultiNavigation: DischargeFragment ViewModel is null")
                        }
                    } catch (e: Exception) {
                        Log.d(TAG, "MultiNavigation: Could not access DischargeFragment ViewModel: ${e.message}")
                    }
                }
            }
        }

        // Log fragment manager state
        val fragmentManager = this.fragmentManager
        if (fragmentManager != null) {
            Log.d(TAG, "FragmentCache: FragmentManager back stack count: ${fragmentManager.backStackEntryCount}")
            Log.d(TAG, "FragmentCache: FragmentManager fragments count: ${fragmentManager.fragments.size}")
        } else {
            Log.d(TAG, "FragmentCache: FragmentManager is null")
        }

        Log.d(TAG, "FragmentCache: === END FRAGMENT CACHE STATE ($context) ===")
    }

    /**
     * Clears the fragment cache and removes fragments from FragmentManager.
     */
    fun clearFragmentCache() {
        val fragmentManager = this.fragmentManager
        if (fragmentManager != null && fragmentCache.isNotEmpty()) {
            Log.d(TAG, "FRAGMENT_CACHE: Clearing fragment cache (${fragmentCache.size} fragments)")

            val transaction = fragmentManager.beginTransaction()
            fragmentCache.values.forEach { fragment ->
                if (fragment.isAdded) {
                    transaction.remove(fragment)
                }
            }
            transaction.commitAllowingStateLoss()
        }

        fragmentCache.clear()
        fragmentCreationCount = 0
        cacheHitCount = 0

        Log.d(TAG, "FRAGMENT_CACHE: Fragment cache cleared")
    }

    /**
     * Removes unknown fragments that might be from Navigation Component.
     */
    fun cleanupUnknownFragments() {
        val fragmentManager = this.fragmentManager ?: return

        // Log ALL fragments in the container to detect Navigation Component conflicts
        val allFragments = fragmentManager.fragments
        Log.d(TAG, "FRAGMENT_CONTAINER_AUDIT: Total fragments in container: ${allFragments.size}")
        allFragments.forEachIndexed { index, fragment ->
            Log.d(TAG, "FRAGMENT_CONTAINER_AUDIT: [$index] ${fragment?.javaClass?.simpleName} - visible: ${fragment?.isVisible}, hidden: ${fragment?.isHidden}, added: ${fragment?.isAdded}")
        }

        // Remove any unknown fragments that might be from Navigation Component
        val unknownFragments = allFragments.filter { fragment ->
            fragment != null && fragment.isAdded && !fragmentCache.values.contains(fragment)
        }

        if (unknownFragments.isNotEmpty()) {
            Log.w(TAG, "FRAGMENT_CONTAINER_AUDIT: Found ${unknownFragments.size} unknown fragments (likely from Navigation Component), removing...")
            val cleanupTransaction = fragmentManager.beginTransaction()
            unknownFragments.forEach { fragment ->
                Log.w(TAG, "FRAGMENT_CONTAINER_AUDIT: Removing unknown fragment: ${fragment?.javaClass?.simpleName}")
                if (fragment != null) {
                    cleanupTransaction.remove(fragment)
                }
            }
            cleanupTransaction.commitNow()
        }
    }

    /**
     * Gets a human-readable fragment name for logging.
     */
    private fun getFragmentName(fragmentId: Int?): String {
        return when (fragmentId) {
            R.id.chargeFragment -> "ChargeFragment"
            R.id.dischargeFragment -> "DischargeFragment"
            R.id.animationGridFragment -> "AnimationGridFragment"
            R.id.othersFragment -> "OthersFragment"
            R.id.healthFragment -> "HealthFragment"
            R.id.settingsFragment -> "SettingsFragment"
            else -> "Unknown($fragmentId)"
        }
    }
}
